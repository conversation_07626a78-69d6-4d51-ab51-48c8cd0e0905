import { Injectable } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { EntitySetup, EntityValue } from '@app/_interfaces/feature.interface';
import { Attachment } from '@app/_interfaces/generic.interface';
import { Habit<PERSON>imerAnswer } from '@app/_interfaces/habit.interface';
import { List } from '@app/_interfaces/list.interface';
import { Note } from '@app/_interfaces/note.interface';
import { Checklist } from '@app/_interfaces/todo.interface';
import { UserViewSettings } from '@app/_interfaces/user.interface';
import { AttachmentType, DayCode, EntityName, FeatureStatus, RepeatType, ShowType } from '@app/_types/generic.type';
import { getDateString, getNewId } from '@app/_utils/utils';
import { CacheService } from './cache.service';
import { ParseMinutesPipe } from '@app/_pipes/parse-minutes.pipe';

@Injectable({
    providedIn: 'root',
})

export class DependencyService {

    weeklyRecord: { [key in DayCode]: EntitySetup[] } = {
        ['SU']: [],
        ['MO']: [],
        ['TU']: [],
        ['WE']: [],
        ['TH']: [],
        ['FR']: [],
        ['SA']: [],
    };
    dayRecord: { [key in string]: EntitySetup[] } = {
        '1': [],
        '2': [],
        '3': [],
        '4': [],
        '5': [],
        '6': [],
        '7': [],
        '8': [],
        '9': [],
        '10': [],
        '11': [],
        '12': [],
        '13': [],
        '14': [],
        '15': [],
        '16': [],
        '17': [],
        '18': [],
        '19': [],
        '20': [],
        '21': [],
        '22': [],
        '23': [],
        '24': [],
        '25': [],
        '26': [],
        '27': [],
        '28': [],
        '29': [],
        '30': [],
        '31': [],
        '-1': [],
    };

    constructor(private fb: FormBuilder, public cc: CacheService) {

    }

    filterLists(lists: List[], searchQuery: string, isFav: boolean, isCompleted: boolean | null, tags: string[]): List[] {
        const query = searchQuery.toLowerCase();

        return lists.filter(item => {
            const matchesFav = !isFav || item.isFav === true;
            const listItems = item.listItems ? Object.values(item.listItems).filter(item => !item.deletedAt) : [];
            const matchesCompleted = item.listItems ? (isCompleted ? Object.values(listItems).every(item => item?.done === true) && listItems.length > 0 : !Object.values(listItems).every(item => item?.done === true) || listItems.length === 0) : false;
            const matchesTags = tags.some(tag => item.hashtags?.includes(tag));

            const matchesQuery = !query || item.title.toLowerCase().includes(query) || (item.description && item.description.toLowerCase().includes(query)); // Apply search query

            return !item.deletedAt && matchesFav && matchesQuery && (isCompleted !== null ? matchesCompleted : true) && (tags.length > 0 ? matchesTags : true);
        });
    }

    filterNotes(notes: Note[], searchQuery: string, isFav: boolean, moods: number[], tags: string[], attachments: AttachmentType[], startDate: Date | null, endDate: Date | null): Note[] {
        const isMood = moods.length > 0;
        const query = searchQuery.toLowerCase();

        return notes.filter(item => {
            const matchesFav = !isFav || item.isFav === true; // Apply isFav condition if true
            const matchesMood = moods.includes(item.emotion);
            const matchesTags = tags.some(tag => item.tags?.includes(tag));
            const matchesAttachments = attachments.every(attType => item.attachments?.some((attachment: Attachment) => attachment.fileType === attType));
            const matchesDate = startDate && endDate
                ? item.noteUpdatedAt !== undefined &&
                item.noteUpdatedAt.getTime() >= startDate.getTime() &&
                item.noteUpdatedAt.getTime() <= endDate.getTime()
                : true;

            const matchesQuery = !query || item.title.toLowerCase().includes(query) || (item.description && item.description.toLowerCase().includes(query)); // Apply search query

            return !item.deletedAt && matchesFav && matchesQuery && (isMood ? matchesMood : true) && (tags.length > 0 ? matchesTags : true) && (attachments.length > 0 ? matchesAttachments : true) && (startDate && endDate ? matchesDate : true);
        });
    }

    addAttachmentForm(value?: Attachment) {
        return this.fb.group({
            id: new FormControl(value ? value.id : Date.now(), Validators.required),
            originalFileName: new FormControl(value ? value.originalFileName : '', Validators.required),
            fileType: new FormControl(value ? value.fileType : '', Validators.required),
            format: new FormControl(value ? value.format : '', Validators.required),
            size: new FormControl(value ? value.size : 0, Validators.required),
            metadata: new FormControl(value ? value.metadata : {}),
            containsThumbnail: new FormControl(value ? value.containsThumbnail : false, Validators.required),
            localPath: new FormControl(value ? value.localPath : ''),
            status: new FormControl(value ? value.status : 'local', Validators.required),
            createdAt: new FormControl(value ? value.createdAt : new Date(), Validators.required),
            deletedAt: new FormControl(value ? value.deletedAt : null),
        });
    }

    addChecklistForm(value?: Checklist) {
        return this.fb.group({
            id: new FormControl(value ? value.id : getNewId(), Validators.required),
            checklistText: new FormControl(value ? value.checklistText : '', [Validators.maxLength(80)]),
            isCompleted: new FormControl(value ? value.isCompleted : false),
        });
    }

    getAttachmentPath(basePath: string, attachment: Attachment, attachmentType: 'originalFiles' | 'optimizedFiles'): string {
        const attachmentName = `${attachment.id}.${attachment.format}`;
        return `${basePath}/${attachmentType}/${attachmentName}`;
    }

    extractOpsString(input: string | null): string {
        if (!input) return "";

        try {
            const parsed = JSON.parse(input);
            if (Array.isArray(parsed)) {
                return JSON.stringify(parsed);
            } else if (parsed && typeof parsed === 'object' && Array.isArray(parsed.ops)) {
                return JSON.stringify(parsed.ops);
            }
        } catch (error) {
            console.error('Invalid JSON input:', error);
        }

        return "";
    }

    extractString(input: string = ""): string {
        if (!input) return "";
        try {
            const parsed = JSON.parse(input);
            const ops = Array.isArray(parsed) ? parsed : parsed && typeof parsed === 'object' && Array.isArray(parsed.ops) ? parsed.ops : [];
            return ops.map((op: { insert: string; }) => (typeof op.insert === 'string' ? op.insert : '')).join('');
        } catch (error) {
            console.error('Invalid JSON input:', error);
            return "";
        }
    };

    getRepeatTypeValue(repeatRule: string): RepeatType {

        // Exact match for daily
        if (repeatRule.includes('FREQ=DAILY')) {
            return 'DAILY';
        }

        // Match Monday to Friday for working days
        if (
            repeatRule.includes('FREQ=WEEKLY') &&
            repeatRule.includes('BYDAY') &&
            repeatRule.includes('MO') &&
            repeatRule.includes('TU') &&
            repeatRule.includes('WE') &&
            repeatRule.includes('TH') &&
            repeatRule.includes('FR')
        ) {
            return 'WEEK_WORKING_DAY';
        }

        // Weekly with other day patterns
        if (repeatRule.includes('FREQ=WEEKLY')) {
            return 'WEEKLY';
        }

        // Monthly with nth weekday (e.g., 2MO, 5TU, etc.)
        if (repeatRule.includes('FREQ=MONTHLY') && repeatRule.includes('BYDAY')) {
            return 'MONTHLY_NTH_DAY';
        }

        // Monthly with last day
        if (
            repeatRule.includes('FREQ=MONTHLY') &&
            repeatRule.includes('BYMONTHDAY=-1')
        ) {
            return 'MONTHLY_LAST_DAY';
        }

        // Monthly with specific days
        if (
            repeatRule.includes('FREQ=MONTHLY') &&
            repeatRule.includes('BYMONTHDAY')
        ) {
            return 'MONTHLY';
        }

        // Yearly
        if (repeatRule.includes('FREQ=YEARLY')) {
            return 'YEARLY';
        }

        return 'OFF'; // default fallback
    }

    toRRuleRecord(rrule: string): Record<string, string> {
        if (!rrule) return {};
        const ruleParts = rrule.replace('RRULE:', '').split(';');
        const ruleRecord: Record<string, string> = {};

        ruleParts.forEach(part => {
            const [key, value] = part.split('=');
            ruleRecord[key] = value;
        });
        return ruleRecord;
    }

    toRRuleString(ruleObj: Record<string, string>): string {
        if (Object.keys(ruleObj).length === 0) {
            return '';
        }
        const parts = Object.entries(ruleObj).map(([key, value]) => `${key}=${value}`);
        return 'RRULE:' + parts.join(';');
    }

    parseDateInfo(dateStr: string): [string, DayCode, string, string, boolean] {
        const date = new Date(dateStr);

        const day = String(date.getDate());
        const weekday = ['SU', 'MO', 'TU', 'WE', 'TH', 'FR', 'SA'][date.getDay()];

        const dayOfMonth = date.getDate();
        const nthWeek = Math.floor((dayOfMonth - 1) / 7) + 1;

        const isLastDay = dayOfMonth === new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();

        const month = String(date.getMonth() + 1);

        return [day, weekday as DayCode, String(nthWeek), month, isLastDay];
    }

    computeRule(entitySetup: EntityValue[], entityName: EntityName): {
        oneDay: { [key in string]: EntitySetup[] },
        daily: EntitySetup[],
        weekly: { [key in DayCode]: EntitySetup[] },
        monthly: { [key in string]: { [key in string]: EntitySetup[] } },
        ordinally: { [key in string]: { [key in DayCode]: EntitySetup[] } }
    } {
        const entityWorker = new Worker(new URL('../_web-workers/rule.worker', import.meta.url));

        let computedOneDay: { [key in string]: EntitySetup[] } = {};
        let computedDaily: EntitySetup[] = [];
        let computedWeekly: { [key in DayCode]: EntitySetup[] } = { ...this.weeklyRecord };
        let computedMonthly: { [key in string]: { [key in string]: EntitySetup[] } } = {
            '1': structuredClone(this.dayRecord),
            '2': structuredClone(this.dayRecord),
            '3': structuredClone(this.dayRecord),
            '4': structuredClone(this.dayRecord),
            '5': structuredClone(this.dayRecord),
            '6': structuredClone(this.dayRecord),
            '7': structuredClone(this.dayRecord),
            '8': structuredClone(this.dayRecord),
            '9': structuredClone(this.dayRecord),
            '10': structuredClone(this.dayRecord),
            '11': structuredClone(this.dayRecord),
            '12': structuredClone(this.dayRecord),
        };
        let computedOrdinally: { [key in string]: { [key in DayCode]: EntitySetup[] } } = {
            '1': structuredClone(this.weeklyRecord),
            '2': structuredClone(this.weeklyRecord),
            '3': structuredClone(this.weeklyRecord),
            '4': structuredClone(this.weeklyRecord),
            '5': structuredClone(this.weeklyRecord),
        };

        entityWorker.postMessage({
            entitySetup,
            entityName
        });

        console.log('Sent to worker::::::::::',entityName, entitySetup);

        // Listen for the worker's response
        entityWorker.onmessage = ({ data }) => {
            console.log('Received from worker::::::::::',entityName, data);
            const { oneDay, daily, weekly, monthly, ordinally } = data;
            computedOneDay = oneDay;
            computedDaily = daily;
            computedWeekly = weekly;
            computedMonthly = monthly;
            computedOrdinally = ordinally;
        };

        // Handle any potential errors from the worker
        entityWorker.onerror = (error) => {
            console.error('Error in Web Worker:', error);
        };

        const result = {
            oneDay: computedOneDay,
            daily: computedDaily,
            weekly: computedWeekly,
            monthly: computedMonthly,
            ordinally: computedOrdinally,
        };

        console.log('Result from worker:::::::::::',entityName, result);

        return result;
    }

    computeRuleCheck(entitySetup: EntityValue[], entityName: EntityName): {
        oneDay: { [key in string]: EntitySetup[] },
        daily: EntitySetup[],
        weekly: { [key in DayCode]: EntitySetup[] },
        monthly: { [key in string]: { [key in string]: EntitySetup[] } },
        ordinally: { [key in string]: { [key in DayCode]: EntitySetup[] } }
    } {
        const oneDay: { [key in string]: EntitySetup[] } = {};
        const daily: EntitySetup[] = [];
        const weekly: { [key in DayCode]: EntitySetup[] } = { ...this.weeklyRecord };
        const monthly: { [key in string]: { [key in string]: EntitySetup[] } } = {
            '1': structuredClone(this.dayRecord),
            '2': structuredClone(this.dayRecord),
            '3': structuredClone(this.dayRecord),
            '4': structuredClone(this.dayRecord),
            '5': structuredClone(this.dayRecord),
            '6': structuredClone(this.dayRecord),
            '7': structuredClone(this.dayRecord),
            '8': structuredClone(this.dayRecord),
            '9': structuredClone(this.dayRecord),
            '10': structuredClone(this.dayRecord),
            '11': structuredClone(this.dayRecord),
            '12': structuredClone(this.dayRecord),
        };
        const ordinally: { [key in string]: { [key in DayCode]: EntitySetup[] } } = {
            '1': structuredClone(this.weeklyRecord),
            '2': structuredClone(this.weeklyRecord),
            '3': structuredClone(this.weeklyRecord),
            '4': structuredClone(this.weeklyRecord),
            '5': structuredClone(this.weeklyRecord),
        };
        this.weeklyRecord = {
            ['SU']: [],
            ['MO']: [],
            ['TU']: [],
            ['WE']: [],
            ['TH']: [],
            ['FR']: [],
            ['SA']: [],
        };
        this.dayRecord = {
            '1': [],
            '2': [],
            '3': [],
            '4': [],
            '5': [],
            '6': [],
            '7': [],
            '8': [],
            '9': [],
            '10': [],
            '11': [],
            '12': [],
            '13': [],
            '14': [],
            '15': [],
            '16': [],
            '17': [],
            '18': [],
            '19': [],
            '20': [],
            '21': [],
            '22': [],
            '23': [],
            '24': [],
            '25': [],
            '26': [],
            '27': [],
            '28': [],
            '29': [],
            '30': [],
            '31': [],
            '-1': [],
        };

        entitySetup.forEach(setup => {
            const entitySetup: EntitySetup = {
                id: setup.id,
                entityName: entityName,
                startAt: setup.startAt,
                endAt: setup.endAt,
                title: setup.title,
                duration: setup.duration,
                invalid: false,
                status: 'all',
                tags: setup.tags || [],
                repeatType: 'all',
                percentage: 0
            };

            try {
                if (setup.deletedAt) return;
                if (!setup.repeat || setup.repeat.length === 0) {
                    oneDay[setup.startAt?.dateString] = oneDay[setup.startAt?.dateString] || [];
                    entitySetup.repeatType = 'one_time';
                    oneDay[setup.startAt?.dateString].push(entitySetup);
                } else {
                    const ruleRecord = this.toRRuleRecord(setup.repeat[0]);
                    switch (ruleRecord['FREQ']) {
                        case 'DAILY':
                            entitySetup.repeatType = 'all';
                            daily.push(entitySetup);
                            break;

                        case 'WEEKLY':
                            const days = ruleRecord['BYDAY'].split(',') as DayCode[];
                            entitySetup.repeatType = 'repeated';
                            days.forEach(day => {
                                weekly[day].push(entitySetup);
                            });

                            break;

                        case 'MONTHLY':
                            if (ruleRecord['BYMONTHDAY'] && ruleRecord['BYMONTH']) {
                                const days = ruleRecord['BYMONTHDAY'].split(',').map(Number);
                                const months = ruleRecord['BYMONTH'].split(',').map(Number);
                                entitySetup.repeatType = 'repeated';
                                days.forEach(day => {
                                    months.forEach(month => {
                                        monthly[month][day].push(entitySetup);
                                    });
                                });
                            } else if (ruleRecord['BYDAY']) {
                                const byDayValues = ruleRecord['BYDAY'].split(',');
                                entitySetup.repeatType = 'repeated';
                                byDayValues.forEach(item => {
                                    const match = item.match(/^(-?\d)?([A-Z]{2})$/);
                                    if (match) {
                                        const [, weekNum, dayCode] = match;
                                        if (weekNum) {
                                            const ordinal = weekNum;
                                            ordinally[ordinal][dayCode as DayCode]?.push(entitySetup);
                                        }
                                    }
                                });
                            }
                            break;

                        case 'YEARLY':
                            const day = ruleRecord['BYMONTHDAY'];
                            const month = ruleRecord['BYMONTH'];
                            entitySetup.repeatType = 'repeated';
                            monthly[month][day].push(entitySetup);
                            break;

                        default:
                            break;
                    }

                }
            } catch (error) {
                console.error('Error parsing repeat rule:', error);
            }
        });

        return {
            oneDay,
            daily,
            weekly,
            monthly,
            ordinally
        };
    }

    getEntityShowValues(viewSettings: UserViewSettings): ShowType[] {
        const todaySettings = viewSettings.todaySettings.todayTabSettings;
        const entityShow: ShowType[] = [];
        if (todaySettings.showMood) {
            entityShow.push('mood');
        }
        if (todaySettings.showTime) {
            entityShow.push('time');
        }
        if (todaySettings.showReminder) {
            entityShow.push('reminder');
        }
        if (todaySettings.showDuration) {
            entityShow.push('duration');
        }
        if (todaySettings.showRepeat) {
            entityShow.push('repeat');
        }
        if (todaySettings.showChecklist) {
            entityShow.push('checklist');
        }
        if (todaySettings.showImage) {
            entityShow.push('attachments');
        }
        if (viewSettings.featureSettings.showFeatureLabels) {
            entityShow.push('label');
        }
        if (todaySettings.showTags) {
            entityShow.push('hashtag');
        }
        if (todaySettings.showInvalidEntries) {
            entityShow.push('invalidEntry');
        }
        return entityShow;
    }

    getShowValues(settings: any): ShowType[] {
        // const pastSettings = viewSettings.pastSettings.pastTodoSettings;
        const entityShow: ShowType[] = [];
        if (settings.showHabitResponse) {
            entityShow.push('habitResponse');
        }
        if (settings.showSetupTitle) {
            entityShow.push('setup');
        }
        if (settings.showMood || settings.noteViewMood) {
            entityShow.push('mood');
        }
        if (settings.showDate || settings.noteViewDate) {
            entityShow.push('date');
        }
        if (settings.showTime || settings.noteViewTime) {
            entityShow.push('time');
        }
        if (settings.showReminder) {
            entityShow.push('reminder');
        }
        if (settings.showDuration) {
            entityShow.push('duration');
        }
        if (settings.showRepeat) {
            entityShow.push('repeat');
        }
        if (settings.showChecklist) {
            entityShow.push('checklist');
        }
        if (settings.showImage || settings.noteViewImage) {
            entityShow.push('attachments');
        }
        if (settings.showLabel) {
            entityShow.push('label');
        }
        if (settings.showDescription) {
            entityShow.push('description');
        }
        if (settings.itemCount) {
            entityShow.push('itemCount');
        }
        if (settings.showMemberCount || settings.showCollaboratorsCount) {
            entityShow.push('collaboratorsCount');
        }
        if (settings.showInvitedUsersCount) {
            entityShow.push('invitedUsersCount');
        }
        if (settings.showAwaitingUserCount) {
            entityShow.push('awaitingUserCount');
        }
        if (settings.showTags || settings.noteViewTags || settings.showHashtags || settings.showHashtag) {
            entityShow.push('hashtag');
        }
        if (settings.showEmptyDays) {
            entityShow.push('emptyDays');
        }
        if (settings.showInvalidEntries) {
            entityShow.push('invalidEntry');
        }
        if (settings.showCalendarName) {
            entityShow.push('calendarName');
        }
        if (settings.showLastUpdatedBy) {
            entityShow.push('lastUpdatedBy');
        }
        if (settings.showLastUpdatedAt) {
            entityShow.push('lastUpdatedAt');
        }
        return entityShow;
    }

    getGroupedViewType(settings: { collapsedView: boolean, showCounts?: boolean, showNetAmount?: boolean }): string[] {
        const entityGroupViewType: string[] = [];
        if (settings.collapsedView) {
            entityGroupViewType.push('collapsedView');
        }
        if (settings.showCounts) {
            entityGroupViewType.push('showCounts');
        }
        if (settings.showNetAmount) {
            entityGroupViewType.push('showNetAmount');
        }
        return entityGroupViewType;
    }

    getFeatureStatus(startDate: string, endDate?: string): FeatureStatus {
        const todayDateString = getDateString();
        const today = new Date(todayDateString);
        today.setHours(0, 0, 0, 0); // normalize today's date (optional)

        const start = new Date(startDate);
        const end = endDate ? new Date(endDate) : null;

        if (startDate === todayDateString) {
            return 'active';
        }

        if (start > today) {
            return 'upcoming';
        }

        if (end && end < today) {
            return 'completed';
        }

        return 'active';
    }

    parseMinutes(isoDuration: string): { year: number, month: number, day: number, hour: number, minute: number, second: number, totalSeconds: number } | null {
        const regex = /P(?:(\d+)Y)?(?:(\d+)M)?(?:(\d+)D)?T?(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/;
        const match = isoDuration.match(regex);

        if (!match) return null;

        const [, year, month, day, hour, minute, second] = match.map(v => parseInt(v || '0', 10));
        const totalSeconds = (hour * 60 * 60) + (minute * 60) + second;

        return { year, month, day, hour, minute, second, totalSeconds };
    }

    getTotalSecondsSpent(timerAnswer: HabitTimerAnswer[] | null): number {
        let totalMs = 0;

        timerAnswer?.forEach((answer: HabitTimerAnswer) => {
            const start = new Date(answer.startTimestamp).getTime();
            const end = new Date(answer.endTimestamp).getTime();
            totalMs += Math.max(0, end - start);
        });

        return Math.floor(totalMs / 1000); // in seconds
    }

    parseMinutesPipe = new ParseMinutesPipe(this.cc);

    getReminderText(reminder: number) {
        if (reminder === 0) {
            return this.cc.texts()['dropdown_remindOption_onTime'];
        } else if (reminder < 0) {
            return this.cc.interpolateText('overlay_reminderSelect_previewBeforeTime', { time: this.parseMinutesPipe.transform(Math.abs(reminder)) });
        } else {
            return this.cc.interpolateText('overlay_reminderSelect_previewAfterTime', { time: this.parseMinutesPipe.transform(reminder) });
        }
    }
}