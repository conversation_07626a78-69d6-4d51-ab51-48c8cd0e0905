{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"angular": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/angular", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js", "src/buffer-polyfill.ts"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "./functions", "output": "./functions", "ignore": ["api/**"]}, {"glob": "**/*", "input": "./.well-known", "output": "./.well-known"}, {"glob": "**/*.worker.ts", "input": "src/app/_web-workers", "output": "/app/_web-workers"}], "styles": ["node_modules/quill/dist/quill.snow.css", "node_modules/@videogular/ngx-videogular/fonts/videogular.css", "node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.carousel.min.css", "node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.theme.default.min.css", "@angular/material/prebuilt-themes/deeppurple-amber.css", "node_modules/swiper/swiper-bundle.min.css", "node_modules/angular-calendar/css/angular-calendar.css", "src/styles.scss"], "scripts": ["node_modules/swiper/swiper-bundle.min.js"], "allowedCommonJsDependencies": ["buffer", "quill-delta", "lodash", "convert-firebase-timestamp", "qr-code-styling", "konva", "crypto-js", "<PERSON><PERSON><PERSON><PERSON>"], "webWorkerTsConfig": "tsconfig.worker.json"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "5mb"}], "optimization": true, "aot": true, "namedChunks": false, "sourceMap": false, "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "staging": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "5mb"}], "optimization": true, "aot": true, "namedChunks": false, "sourceMap": false, "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}]}, "qa": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "5mb"}], "optimization": true, "aot": true, "namedChunks": false, "sourceMap": false, "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.qa.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "development"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "angular:build:production"}, "staging": {"buildTarget": "angular:build:staging"}, "qa": {"buildTarget": "angular:build:qa"}, "development": {"buildTarget": "angular:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "angular:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}