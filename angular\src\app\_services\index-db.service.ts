import { Injectable } from '@angular/core';
import { NgxIndexedDBService } from 'ngx-indexed-db';
import { FirebaseService } from './firebase.service';
import {
    BehaviorSubject,
    catchError,
    debounceTime,
    firstValueFrom,
    from,
    interval,
    lastValueFrom,
    mergeMap,
    Observable,
    of,
    startWith,
    Subject,
    switchMap,
    take,
    takeUntil,
    throwError
} from 'rxjs';
import * as _ from 'lodash';
import { dbConfig } from '@app/_configs/db.config';
import { Collection } from '@app/_types/collection.type';
import { List } from '@app/_interfaces/list.interface';
import { StorageService } from './storage.servive';
import { Note } from '@app/_interfaces/note.interface';
import { FbEntity, JsEntity } from '@app/_interfaces/entity.interface';
import { UserMetaData, UserResource, UserViewSettings } from '@app/_interfaces/user.interface';
import { Journal, JournalSetup } from '@app/_interfaces/journal.interface';
import { Todo } from '@app/_interfaces/todo.interface';
import { Habit, HabitSetup } from '@app/_interfaces/habit.interface';
import { MoneyTransaction, MoneyTrackerSetup } from '@app/_interfaces/money-tracker.interface';
import { CalendarEvent, CalendarEventSetup, CalendarIntegration } from '@app/_interfaces/calendar-integration.interface';

@Injectable({
    providedIn: 'root'
})

export class IndexDbService {

    todoSubject$ = new BehaviorSubject<Todo[] | null>(null);
    todos$: Observable<Todo[] | null> = this.todoSubject$.asObservable();

    listSubject$ = new BehaviorSubject<List[] | null>(null);
    lists$: Observable<List[] | null> = this.listSubject$.asObservable();

    noteSubject$ = new BehaviorSubject<Note[] | null>(null);
    notes$: Observable<Note[] | null> = this.noteSubject$.asObservable();

    journalSetupSubject$ = new BehaviorSubject<JournalSetup[] | null>(null);
    journalSetups$: Observable<JournalSetup[] | null> = this.journalSetupSubject$.asObservable();
    journalSubject$ = new BehaviorSubject<Journal[] | null>(null);
    journals$: Observable<Journal[] | null> = this.journalSubject$.asObservable();

    habitSetupSubject$ = new BehaviorSubject<HabitSetup[] | null>(null);
    habitSetups$: Observable<HabitSetup[] | null> = this.habitSetupSubject$.asObservable();
    habitSubject$ = new BehaviorSubject<Habit[] | null>(null);
    habits$: Observable<Habit[] | null> = this.habitSubject$.asObservable();

    moneyTrackerSetupSubject$ = new BehaviorSubject<MoneyTrackerSetup[] | null>(null);
    moneyTrackerSetups$: Observable<MoneyTrackerSetup[] | null> = this.moneyTrackerSetupSubject$.asObservable();
    moneyTransactionSubject$ = new BehaviorSubject<MoneyTransaction[] | null>(null);
    moneyTransaction$: Observable<MoneyTransaction[] | null> = this.moneyTransactionSubject$.asObservable();

    calendarIntegrationsSubject$ = new BehaviorSubject<CalendarIntegration[] | null>(null);
    calendarIntegrations$: Observable<CalendarIntegration[] | null> = this.calendarIntegrationsSubject$.asObservable();
    calendarEventSetupSubject$ = new BehaviorSubject<CalendarEventSetup[] | null>(null);
    calendarEventSetups$: Observable<CalendarEventSetup[] | null> = this.calendarEventSetupSubject$.asObservable();
    calendarEventSubject$ = new BehaviorSubject<CalendarEvent[] | null>(null);
    calendarEvents$: Observable<CalendarEvent[] | null> = this.calendarEventSubject$.asObservable();

    userResourceSubject$ = new BehaviorSubject<UserResource[] | null>(null);
    userResource$: Observable<UserResource[] | null> = this.userResourceSubject$.asObservable();
    userMetadataSubject$ = new BehaviorSubject<UserMetaData[] | null>(null);
    userMetadata$: Observable<UserMetaData[] | null> = this.userMetadataSubject$.asObservable();
    viewSettingsSubject$ = new BehaviorSubject<UserViewSettings[] | null>(null);
    viewSettings$: Observable<UserViewSettings[] | null> = this.viewSettingsSubject$.asObservable();

    private fetchTriggers: { [key: string]: Subject<void> } = {
        todos: new Subject<void>(),
        lists: new Subject<void>(),
        notes: new Subject<void>(),
        userResources: new Subject<void>(),
        usersMetadata: new Subject<void>(),
        viewSettings: new Subject<void>(),
        journalSetups: new Subject<void>(),
        journalActions: new Subject<void>(),
        habitSetups: new Subject<void>(),
        habitActions: new Subject<void>(),
        moneyTrackerSetups: new Subject<void>(),
        moneyTrackerTransactions: new Subject<void>(),
        calendarIntegrations: new Subject<void>(),
        calendarEventSetups: new Subject<void>(),
        calendarEventActions: new Subject<void>(),
        specialActivities: new Subject<void>()
    };
    private latestUpdatedAts: { [key: string]: Date } = {
        todos: new Date(2000, 0, 1),
        lists: new Date(2000, 0, 1),
        notes: new Date(2000, 0, 1),
        userResources: new Date(2000, 0, 1),
        usersMetadata: new Date(2000, 0, 1),
        viewSettings: new Date(2000, 0, 1),
        journalSetups: new Date(2000, 0, 1),
        journalActions: new Date(2000, 0, 1),
        habitSetups: new Date(2000, 0, 1),
        habitActions: new Date(2000, 0, 1),
        moneyTrackerSetups: new Date(2000, 0, 1),
        moneyTrackerTransactions: new Date(2000, 0, 1),
        calendarIntegrations: new Date(2000, 0, 1),
        calendarEventSetups: new Date(2000, 0, 1),
        calendarEventActions: new Date(2000, 0, 1)
    };

    private CollabfetchTriggers: { [key: string]: Subject<void> } = {
        todos: new Subject<void>(),
        lists: new Subject<void>(),
        notes: new Subject<void>(),
        journalSetups: new Subject<void>(),
        journalActions: new Subject<void>(),
        habitSetups: new Subject<void>(),
        habitActions: new Subject<void>(),
        moneyTrackerSetups: new Subject<void>(),
        moneyTrackerTransactions: new Subject<void>(),
    };

    private CollablatestUpdatedAts: { [key: string]: Date } = {
        todos: new Date(2000, 0, 1),
        lists: new Date(2000, 0, 1),
        notes: new Date(2000, 0, 1),
        journalSetups: new Date(2000, 0, 1),
        journalActions: new Date(2000, 0, 1),
        habitSetups: new Date(2000, 0, 1),
        habitActions: new Date(2000, 0, 1),
        moneyTrackerSetups: new Date(2000, 0, 1),
        moneyTrackerTransactions: new Date(2000, 0, 1),
    };

    private collections: Collection[] = [
        'viewSettings',
        'todos',
        'lists',
        'notes',
        'journalSetups',
        'journalActions',
        'habitSetups',
        'habitActions',
        'moneyTrackerSetups',
        'moneyTrackerTransactions',
        'calendarIntegrations',
        'calendarEventSetups',
        'calendarEventActions',
        'userResources',
        'usersMetadata'
    ];

    private collectionsWithCollab: Collection[] = [
        'todos',
        'habitSetups',
        'habitActions',
        'journalSetups',
        'journalActions',
        'moneyTrackerSetups',
        'moneyTrackerTransactions',
        'notes',
        'lists'
    ];

    private collectionsOnlyFromCloud: Collection[] = [
        'specialActivities'
    ];

    unSubscribe = new Subject<void>();

    constructor(
        private dbService: NgxIndexedDBService,
        private fbService: FirebaseService,
        private ss: StorageService,
    ) {
        this.setupDatabaseChangeListener();
    }

    onInit() {
        console.log('INITIALISING');
        const collections = [
            ...this.collections.map(collection => ({ collection, withCollab: false })),
            ...this.collectionsWithCollab.map(collection => ({ collection, withCollab: true }))
        ];
        console.log('collections', collections);
        const initPromises = collections.map(async ({ collection, withCollab }) => {
            const latestDate = await this.getLatestCloudUpdatedAt(collection as Collection);
            console.log(withCollab ? 'COLLAB LATEST UPDATED AT' : 'MAIN LATEST UPDATED AT', collection, latestDate);
            withCollab ? this.CollablatestUpdatedAts[collection] = latestDate : this.latestUpdatedAts[collection] = latestDate;
            await this.syncDbData(collection);
            (withCollab ? this.CollabfetchTriggers : this.fetchTriggers)[collection].pipe(switchMap(() => this.listernLatestCloudData(collection, withCollab)), takeUntil(this.unSubscribe)).subscribe();
        });

        Promise.all(initPromises).then(async () => {
            console.log('INITIALISED');
            // To take care of offline situations
            let lastCheckTime = new Date();
            let initialised = false;
            interval(1000 * 60)
                .pipe(startWith(0), takeUntil(this.unSubscribe)) // This makes it start immediately
                .subscribe(() => {
                    const currentTime = new Date();
                    const timeDiff = currentTime.getTime() - lastCheckTime.getTime();
                    // if diff greater than 2 mins then check for new items
                    if (timeDiff > 1000 * 60 * 2 || !initialised) {
                        console.log('CHECKING FOR NEW ITEMS');
                        initialised = true;
                        lastCheckTime = currentTime;
                        collections.forEach(({ collection, withCollab }) => {
                            (withCollab ? this.CollabfetchTriggers : this.fetchTriggers)[collection].next();
                        });
                    } else {
                        lastCheckTime = currentTime;
                    }
                });
        });
    }

    // Only triggers when the app needs a clean start (e.g. when user gets newer version of the app))
    async resync() {
        console.log('RESYNCING');
        await this.resyncAllStores();
        this.ss.setResynced(true);
    }

    listernLatestCloudData(collection: Collection, isCollab = false): Observable<JsEntity[]> {
        console.log('FETCHING LATEST DATA - ' + collection);
        let baseObservable: Observable<JsEntity[]>;
        switch (collection) {
            case 'usersMetadata':
                baseObservable = this.fbService.getUserMetadata<FbEntity, UserResource>(collection, this.latestUpdatedAts[collection]);
                break;

            case 'specialActivities':
                baseObservable = this.fbService.getSpecialActivities<FbEntity, any>(collection, this.latestUpdatedAts[collection]);
                break;

            default:
                baseObservable = !isCollab ? this.fbService.getAllItems<FbEntity, JsEntity>(collection, this.latestUpdatedAts[collection]) : this.fbService.getAllCollaborationItems<FbEntity, JsEntity>(collection, this.CollablatestUpdatedAts[collection]);
                break;
        }

        return baseObservable.pipe(
            debounceTime(1000),
            mergeMap(async (items: JsEntity[]) => {
                console.log('FIRESTORE READ - ' + collection, items.length);
                if (items.length > 0) {
                    collection === 'specialActivities' ? this.updateSpecialActivity(items as any[]) : await this.updateBulk(collection, items);
                }
                console.log('FIRESTORE READ - ' + collection, items.length);
                const latestUpdatedItem = _.maxBy(items, 'cloudUpdatedAt');
                if (latestUpdatedItem && latestUpdatedItem.cloudUpdatedAt && latestUpdatedItem.cloudUpdatedAt > (isCollab ? this.CollablatestUpdatedAts[collection] : this.latestUpdatedAts[collection])) {
                    isCollab ? this.CollablatestUpdatedAts[collection] = latestUpdatedItem.cloudUpdatedAt as Date : this.latestUpdatedAts[collection] = latestUpdatedItem.cloudUpdatedAt as Date;
                    isCollab ? this.CollabfetchTriggers[collection].next() : this.fetchTriggers[collection].next();
                }
                return items;
            }),
            catchError(e => {
                console.error('ERROR in ' + collection + ' fetchLatestData');
                console.log('error', e);
                return throwError(() => new Error('ups something happened'));
            })
        );
    }

    async resyncAllStores() {
        const promises = this.collections.map(async (collection) => {
            this.clearStoreObjects(collection);

            let mainItems$: Observable<JsEntity[]>;
            switch (collection) {
                case 'usersMetadata':
                    mainItems$ = this.fbService.getUserMetadata(collection).pipe(take(1));
                    break;

                case 'specialActivities':
                    mainItems$ = this.fbService.getSpecialActivities(collection).pipe(take(1));
                    break;

                default:
                    mainItems$ = this.fbService.getAllItems(collection).pipe(take(1));
                    break;
            }

            mainItems$ = collection === 'usersMetadata' ? this.fbService.getUserMetadata(collection).pipe(take(1)) : this.fbService.getAllItems(collection).pipe(take(1));
            const collabItems$ = this.collectionsWithCollab.includes(collection) ? this.fbService.getAllCollaborationItems(collection).pipe(take(1)) : of([]); // Return empty array if collab not needed

            const [items = [], collabItems = []] = await Promise.all([
                lastValueFrom(mainItems$),
                lastValueFrom(collabItems$)
            ]);

            const allItems = [...items, ...collabItems];
            this.addBulk(collection, allItems);
            console.log(`FIRESTORE RESYNC ${collection} - ${allItems.length}`);
        });

        await Promise.all(promises);
    }

    async getLatestCloudUpdatedAt<T extends { cloudUpdatedAt: Date }>(store: Collection): Promise<Date> {
        if (this.collectionsOnlyFromCloud.includes(store)) return new Date(2000, 0, 1);
        const items = await firstValueFrom(this.dbService.getAll<T>(store));

        return items.reduce((latest, item) =>
            item.cloudUpdatedAt > latest ? item.cloudUpdatedAt : latest,
            new Date(2000, 0, 1)
        );
    }

    // Syncing 

    async syncDbData(collection: Collection) {
        if (this.collectionsOnlyFromCloud.includes(collection)) return;
        const entities = await this.getAll<JsEntity>(collection) || [];
        console.log(`${collection} shown from IDB`, entities.length);
        switch (collection) {
            case 'todos':
                this.todoSubject$.next(entities as Todo[]);
                break;
            case 'lists':
                this.listSubject$.next(entities as List[]);
                break;
            case 'notes':
                this.noteSubject$.next(entities as Note[]);
                break;
            case 'journalSetups':
                this.journalSetupSubject$.next(entities as JournalSetup[]);
                break;
            case 'journalActions':
                this.journalSubject$.next(entities as Journal[]);
                break;
            case 'habitSetups':
                this.habitSetupSubject$.next(entities as HabitSetup[]);
                break;
            case 'habitActions':
                this.habitSubject$.next(entities as Habit[]);
                break;
            case 'moneyTrackerSetups':
                this.moneyTrackerSetupSubject$.next(entities as MoneyTrackerSetup[]);
                break;
            case 'moneyTrackerTransactions':
                this.moneyTransactionSubject$.next(entities as MoneyTransaction[]);
                break;
            case 'calendarIntegrations':
                this.calendarIntegrationsSubject$.next(entities as CalendarIntegration[]);
                break;
            case 'calendarEventSetups':
                this.calendarEventSetupSubject$.next(entities as CalendarEventSetup[]);
                break;
            case 'calendarEventActions':
                this.calendarEventSubject$.next(entities as CalendarEvent[]);
                break;
            case 'userResources':
                this.userResourceSubject$.next(entities as UserResource[]);
                break;
            case 'usersMetadata':
                this.userMetadataSubject$.next(entities as UserMetaData[]);
                break;
            case 'viewSettings':
                this.viewSettingsSubject$.next(entities as UserViewSettings[]);
                break;
        }
    }

    updateSpecialActivity(items: { entityDocCollection: Collection; actionType: 'remove' | 'delete' | 'update'; entityIds: string[], createdAt: Date }[]) {
        items.map((item) => {
            console.log('specal activity item', item);
            switch(item.entityDocCollection) {
                case 'todos':
                    if(item.actionType === 'remove') {
                        const removedEntityIds = item.entityIds.map(async (id) => {
                            const entity = await this.getByKey('todos', id);
                            return this.canRemove(entity, item.createdAt) ? id : null;
                        });
                        this.bulkDelete('todos', removedEntityIds);
                    };
                    break;
            }
        });
    }

    canRemove(model: JsEntity, createdAt: Date): boolean {
        return (
            (model.cloudUpdatedAt ? true : false) && new Date(createdAt) > new Date(model.cloudUpdatedAt instanceof Date ? model.cloudUpdatedAt : 0)
        );
    }

    // Index db related stuffs

    initializeDatabase() {
        dbConfig.objectStoresMeta.forEach((storeConfig) => {
            this.dbService.createObjectStore(storeConfig);
        });
    }

    getEntityById(storeName: Collection, id: string | number): Observable<JsEntity> {
        return from(this.dbService.getByID(storeName, id) as Observable<JsEntity>);
    }

    async deleteDatabase() {
        return await lastValueFrom(this.dbService.deleteDatabase());
    }

    clearStoreObjects(storeName: Collection) {
        this.dbService.clear(storeName);
        // return await lastValueFrom(this.dbService.clear(storeName));
    }

    async deleteStore(storeName: Collection) {
        return await lastValueFrom(this.dbService.deleteObjectStore(storeName));
    }

    async createObjectStore(storeName: Collection) {
        return await this.dbService.createObjectStore({
            store: storeName,
            storeConfig: { keyPath: 'id', autoIncrement: false },
            storeSchema: [{ name: 'id', keypath: 'id', options: { unique: true } }]
        });
    }

    async getAll<T extends JsEntity>(storeName: Collection | 'computed'): Promise<T[]> {
        return lastValueFrom<T[]>(this.dbService.getAll<T>(storeName));
    }

    async getByKey(storeName: Collection, key: any): Promise<JsEntity> {
        return await lastValueFrom(this.dbService.getByKey(storeName, key));
    }

    async getByIndex(storeName: Collection, index: string, key: any) {
        return await lastValueFrom(this.dbService.getByIndex(storeName, index, key));
    }

    addBulk(storeName: Collection, values: JsEntity[]) {
        if (this.collectionsOnlyFromCloud.includes(storeName)) return;
        this.dbService.bulkAdd(storeName, values);
    }

    async add(storeName: Collection, value: JsEntity): Promise<JsEntity> {
        const addedEntity = await lastValueFrom(this.dbService.add(storeName, value));
        this.syncDbData(storeName);
        return addedEntity;
    }

    async update(storeName: Collection, id: string, value: JsEntity): Promise<JsEntity> {
        const updatedEntity = await firstValueFrom(this.dbService.update(storeName, { ...value, id }));
        this.syncDbData(storeName);
        return updatedEntity;
    }

    async updateBulk(storeName: Collection, values: JsEntity[]): Promise<(string)> {
        const updatedKeys = await lastValueFrom(this.dbService.bulkPut(storeName, values));
        this.syncDbData(storeName);
        return updatedKeys.toString();
    }

    async getBulk(storeName: Collection, keys: any[]) {
        return await lastValueFrom(this.dbService.bulkGet(storeName, keys));
    }

    async delete(storeName: Collection, key: any) {
        return await lastValueFrom(this.dbService.delete(storeName, key));
    }

    async bulkDelete(storeName: Collection, keys: any[]) {
        return await lastValueFrom(this.dbService.bulkDelete(storeName, keys));
    }

    async count(storeName: Collection) {
        return await lastValueFrom(this.dbService.count(storeName));
    }

    async openCursor(storeName: Collection) {
        return await lastValueFrom(this.dbService.openCursor(storeName));
    }

    deleteIndexDb() {
        this.dbService.deleteDatabase().pipe(takeUntil(this.unSubscribe))?.subscribe({
            next: (result) => {
                console.log('Database deleted successfully', result);
            },
            error: (error) => {
                console.error('Error deleting database:', error);
            }
        });
    }

    stopSubscription() {
        this.unSubscribe?.next();
        this.unSubscribe?.complete();
    }

    setupDatabaseChangeListener() {
        const dbRequest = indexedDB.open(dbConfig.name, dbConfig.version);

        dbRequest.onsuccess = (event: any) => {
            const db = event.target.result;

            db.onversionchange = () => {
                console.log('Database version changed.');
                // this.handleDatabaseChange();
            };
        };

        dbRequest.onerror = (event) => {
            console.error('Error opening database:', event);
        };
    }

    handleDatabaseChange() {
        // Pull data based on specific conditions
        this.dbService.getAll('lists').subscribe((items: any[]) => {
            // const filteredItems = items.filter(item => item.value > 10); // Example condition
            console.log('Filtered Items:', items);
        });
    }
}
