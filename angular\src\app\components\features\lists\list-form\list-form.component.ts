import { CommonModule } from '@angular/common';
import { Component, computed, inject, Inject, Signal, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  NgForm,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import { <PERSON><PERSON>orm<PERSON>ield, <PERSON><PERSON>abel } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatTooltip } from '@angular/material/tooltip';
import { CdkTextareaAutosize } from '@angular/cdk/text-field';
import { Subject, takeUntil } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { ListStore } from '@app/_stores/list.store';
import { CacheService } from '@app/_services/cache.service';
import { AlertService } from '@app/_services/alert.service';
import { InputTextComponent } from '@app/components/shared/inputs/input-text/input-text.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { List } from '@app/_interfaces/list.interface';
import * as _ from 'lodash';
import { HashtagComponent } from '@app/components/addons/hashtags/hashtag/hashtag.component';
import { UtilsService } from '@app/_services/utils.service';
import { InputHashtagComponent } from '@app/components/shared/inputs/input-hashtag/input-hashtag.component';
import { notOnlyWhitespace } from '@app/_directives/form-validator.directive';

@Component({
  selector: 'app-list-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormField,
    MatLabel,
    MatInput,
    MatTooltip,
    CdkTextareaAutosize,
    MatDialogTitle,
    InputTextComponent,
    InputHashtagComponent,
    SvgComponent
  ],
  templateUrl: './list-form.component.html',
  styleUrl: './list-form.component.scss'
})

export class ListFormComponent {

  @ViewChild('liForm') liForm!: NgForm;
  unSubscribe = new Subject<void>();
  listForm: FormGroup;
  listInitial: List;
  readonly listStore = inject(ListStore);
  isDescription: boolean = false;

  nextPosition: Signal<number> = computed(() => {
    const lists = this.listStore.lists();
    if (lists.length === 0) {
      return 0;
    } else {
      return lists.reduce(
        (max, list) => list.position > max ? list.position : max,
        0
      );
    }
  });

  constructor(
    public dialogRef: MatDialogRef<ListFormComponent>,
    private alertService: AlertService,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', value: List },
    private fb: FormBuilder,
    public cc: CacheService,
    public dialog: MatDialog,
    private utilsService: UtilsService
  ) {
    this.listInitial = data.mode == 'new' ? this.initiateForm() : this.initiateForm(data.value);

    if (this.listInitial.description) {
      this.isDescription = true;
    }

    this.listForm = this.fb.group({
      id: new FormControl(this.listInitial.id, Validators.required),
      title: new FormControl(this.listInitial.title, [Validators.required, Validators.maxLength(120), notOnlyWhitespace()]),
      description: new FormControl(this.listInitial.description, [Validators.maxLength(200), notOnlyWhitespace()]),
      hashtags: new FormControl(this.listInitial.hashtags),
      uid: new FormControl(this.listInitial.uid, Validators.required),
      listItems: new FormControl(this.listInitial.listItems),
      position: new FormControl(this.listInitial.position, Validators.required),
    });

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  initiateForm(list?: List): List {
    return {
      id: list ? list.id : this.utilsService.getNewId(),
      title: list ? list.title : '',
      description: list ? list.description : '',
      hashtags: list ? list.hashtags : [],
      uid: this.cc.user.uid,
      listItems: list ? list.listItems : {},
      position: list ? list.position : 1000,
    }
  }

  getFc(fcName: string): FormControl {
    return this.listForm.get(fcName) as FormControl;
  }

  hasChanges() {
    const initial = _.cloneDeep(this.listInitial);
    const current = _.cloneDeep(this.listForm.value);
    return !_.isEqual(initial, current);
  }

  async save() {
    const listData: List = this.data.mode === 'new' ? this.listStore.getNewList() : this.data.value;
    const updatedList = { ...listData, ...this.listForm.value };

    if (this.data.mode === 'new') {
      updatedList.position = this.nextPosition() + 1000;
      this.listStore.addList(updatedList);
    } else if (this.data.mode === 'edit') {
      this.listStore.updateLists([updatedList]);
    }
    this.dialogRef.close();
  }

  openHashtagsDialog() {
    const dialog = this.dialog.open(HashtagComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        values: this.listForm.value.hashtags,
        type: 'map'
      },
    })

    dialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.listForm.get('hashtags')?.setValue(result);
      }
    });
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

  reset() {
    this.listForm.reset();
    this.liForm.resetForm();
    this.listForm.patchValue(this.listInitial);
  }

  ngOnDestroy() {
    this.unSubscribe.complete();
    this.unSubscribe.next();
  }

}
