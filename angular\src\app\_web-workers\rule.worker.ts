/// <reference lib="webworker" />

import { EntitySetup, EntityValue } from "@app/_interfaces/feature.interface";
import { DayCode } from "@app/_types/generic.type";

let weeklyRecord: { [key in DayCode]: EntitySetup[] } = {
    ['SU']: [],
    ['MO']: [],
    ['TU']: [],
    ['WE']: [],
    ['TH']: [],
    ['FR']: [],
    ['SA']: [],
};
let dayRecord: { [key in string]: EntitySetup[] } = {
    '1': [],
    '2': [],
    '3': [],
    '4': [],
    '5': [],
    '6': [],
    '7': [],
    '8': [],
    '9': [],
    '10': [],
    '11': [],
    '12': [],
    '13': [],
    '14': [],
    '15': [],
    '16': [],
    '17': [],
    '18': [],
    '19': [],
    '20': [],
    '21': [],
    '22': [],
    '23': [],
    '24': [],
    '25': [],
    '26': [],
    '27': [],
    '28': [],
    '29': [],
    '30': [],
    '31': [],
    '-1': [],
};

addEventListener('message', ({ data }) => {
    const { entitySetup, entityName } = data;


    const oneDay: { [key in string]: EntitySetup[] } = {};
    const daily: EntitySetup[] = [];
    const weekly: { [key in DayCode]: EntitySetup[] } = { ...weeklyRecord };
    const monthly: { [key in string]: { [key in string]: EntitySetup[] } } = {
        '1': structuredClone(dayRecord),
        '2': structuredClone(dayRecord),
        '3': structuredClone(dayRecord),
        '4': structuredClone(dayRecord),
        '5': structuredClone(dayRecord),
        '6': structuredClone(dayRecord),
        '7': structuredClone(dayRecord),
        '8': structuredClone(dayRecord),
        '9': structuredClone(dayRecord),
        '10': structuredClone(dayRecord),
        '11': structuredClone(dayRecord),
        '12': structuredClone(dayRecord),
    };
    const ordinally: { [key in string]: { [key in DayCode]: EntitySetup[] } } = {
        '1': structuredClone(weeklyRecord),
        '2': structuredClone(weeklyRecord),
        '3': structuredClone(weeklyRecord),
        '4': structuredClone(weeklyRecord),
        '5': structuredClone(weeklyRecord),
    };

    entitySetup.forEach((setup: EntityValue) => {
        const entitySetup: EntitySetup = {
            id: setup.id,
            entityName: entityName,
            startAt: setup.startAt,
            endAt: setup.endAt,
            title: setup.title,
            duration: setup.duration,
            invalid: false,
            status: 'all',
            tags: setup.tags || [],
            repeatType: 'all',
            percentage: 0
        };

        try {
            if (setup.deletedAt) return;
            if (!setup.repeat || setup.repeat.length === 0) {
                oneDay[setup.startAt?.dateString] = oneDay[setup.startAt?.dateString] || [];
                entitySetup.repeatType = 'one_time';
                oneDay[setup.startAt?.dateString].push(entitySetup);
            } else {
                const ruleRecord = toRRuleRecord(setup.repeat[0]);
                switch (ruleRecord['FREQ']) {
                    case 'DAILY':
                        entitySetup.repeatType = 'all';
                        daily.push(entitySetup);
                        break;

                    case 'WEEKLY':
                        const days = ruleRecord['BYDAY'].split(',') as DayCode[];
                        entitySetup.repeatType = 'repeated';
                        days.forEach(day => {
                            weekly[day].push(entitySetup);
                        });

                        break;

                    case 'MONTHLY':
                        if (ruleRecord['BYMONTHDAY'] && ruleRecord['BYMONTH']) {
                            const days = ruleRecord['BYMONTHDAY'].split(',').map(Number);
                            const months = ruleRecord['BYMONTH'].split(',').map(Number);
                            entitySetup.repeatType = 'repeated';
                            days.forEach((day: number) => {
                                months.forEach((month: number) => {
                                    monthly[month][day].push(entitySetup);
                                });
                            });
                        } else if (ruleRecord['BYDAY']) {
                            const byDayValues = ruleRecord['BYDAY'].split(',');
                            entitySetup.repeatType = 'repeated';
                            byDayValues.forEach((item: string) => {
                                const match = item.match(/^(-?\d)?([A-Z]{2})$/);
                                if (match) {
                                    const [, weekNum, dayCode] = match;
                                    if (weekNum) {
                                        const ordinal = weekNum;
                                        ordinally[ordinal][dayCode as DayCode]?.push(entitySetup);
                                    }
                                }
                            });
                        }
                        break;

                    case 'YEARLY':
                        const day = ruleRecord['BYMONTHDAY'];
                        const month = ruleRecord['BYMONTH'];
                        entitySetup.repeatType = 'repeated';
                        monthly[month][day].push(entitySetup);
                        break;

                    default:
                        break;
                }

            }
        } catch (error) {
            console.error('Error parsing repeat rule:', error);
        }
    });

    postMessage({ oneDay, daily, weekly, monthly, ordinally });
});

function toRRuleRecord(rrule: string): Record<string, string> {
    if (!rrule) return {};
    const ruleParts = rrule.replace('RRULE:', '').split(';');
    const ruleRecord: Record<string, string> = {};

    ruleParts.forEach(part => {
        const [key, value] = part.split('=');
        ruleRecord[key] = value;
    });
    return ruleRecord;
}
