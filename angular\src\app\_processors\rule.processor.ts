import { EntitySetup, EntityValue } from "@app/_interfaces/feature.interface";
import { DayCode, EntityName } from "@app/_types/generic.type";

export class RuleProcessor {
    private entityWorker: Worker | null = null;
    private messageQueue: Map<string, {
        resolve: (value: any) => void,
        reject: (reason: any) => void
    }> = new Map();

    private initializeWorker() {
        if (this.entityWorker) return;

        this.entityWorker = new Worker(new URL('../_web-workers/rule.worker', import.meta.url));

        this.entityWorker.onmessage = ({ data }) => {
            const { messageId, result, entityName } = data;
            console.log('Received from worker::::::::::', entityName, result);

            const pendingMessage = this.messageQueue.get(messageId);
            if (pendingMessage) {
                pendingMessage.resolve(result);
                this.messageQueue.delete(messageId);
            }
        };

        this.entityWorker.onerror = (error) => {
            console.error('Error in Web Worker:', error);
            // Reject all pending promises
            this.messageQueue.forEach(({ reject }) => reject(error));
            this.messageQueue.clear();
        };
    }

    async computeRule(entitySetup: EntityValue[], entityName: EntityName): Promise<{
        oneDay: { [key in string]: EntitySetup[] },
        daily: EntitySetup[],
        weekly: { [key in DayCode]: EntitySetup[] },
        monthly: { [key in string]: { [key in string]: EntitySetup[] } },
        ordinally: { [key in string]: { [key in DayCode]: EntitySetup[] } }
    }> {
        this.initializeWorker();

        const messageId = `${entityName}-${Date.now()}-${Math.random()}`;

        return new Promise((resolve, reject) => {
            this.messageQueue.set(messageId, { resolve, reject });

            this.entityWorker!.postMessage({
                messageId,
                entitySetup,
                entityName
            });

            console.log('Sent to worker::::::::::', entityName, entitySetup);
        });
    }

    terminate() {
        if (this.entityWorker) {
            this.entityWorker.terminate();
            this.entityWorker = null;
        }
        this.messageQueue.clear();
    }
}